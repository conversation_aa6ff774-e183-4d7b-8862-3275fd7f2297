# =============================================================================
# API KEYS
# =============================================================================

ANTHROPIC_API_KEY=
PIEXLDRAIN_API_KEY=
PUBLIC_DISCORD_WEBHOOK=
DEV_DISCORD_WEBHOOK=
DISCORD_EMBED_WEBHOOK=
SPLENDOUR_CAFE_API_KEY=
SPLENDOUR_LOGIN=
SPLENDOUR_PASSWORD=
SUPABASE_ANON_KEY=
SUPABASE_PROJECT_URL=

# =============================================================================
# AI MODEL CONFIGURATION
# =============================================================================

# Translation Model Configuration
# Model used for main translation tasks
# Available options: claude-3-5-sonnet-20241022, claude-3-5-sonnet-20240620, claude-3-opus-20240229, claude-3-sonnet-20240229, claude-3-haiku-20240307
TRANSLATION_MODEL=claude-3-5-sonnet-20241022
TRANSLATION_MAX_TOKENS=8192
TRANSLATION_TEMPERATURE=1.0

# Verification Model Configuration
# Model used for translation verification and quality analysis
# Recommended: claude-sonnet-4-20250514 (Claude 4 with tool use capabilities)
VERIFICATION_MODEL=claude-sonnet-4-20250514
VERIFICATION_MAX_TOKENS=8192
VERIFICATION_TEMPERATURE=0.3

# Improvement Model Configuration
# Model used for improving translations based on verification feedback
# Recommended: claude-3-5-sonnet-20241022 or claude-3-opus-20240229
IMPROVEMENT_MODEL=claude-3-5-sonnet-20241022
IMPROVEMENT_MAX_TOKENS=8192
IMPROVEMENT_TEMPERATURE=0.7

# =============================================================================
# VERIFICATION SYSTEM CONFIGURATION
# =============================================================================

# Enable/disable Claude 4 verification system
CLAUDE4_VERIFICATION_ENABLED=true

# Verification mode: 'full', 'sample', or 'disabled'
# - full: Verify every chunk
# - sample: Verify a percentage of chunks (see CLAUDE4_SAMPLE_RATE)
# - disabled: No verification
CLAUDE4_VERIFICATION_MODE=full

# Sample rate for verification when in 'sample' mode (0.0 to 1.0)
CLAUDE4_SAMPLE_RATE=0.2

# Quality threshold - single condition based on average of all scores (0.0 to 1.0)
# If average score is below this threshold, verification fails and improvement is attempted
MIN_AVERAGE_SCORE=0.75

# Verification retry settings
MAX_VERIFICATION_RETRIES=3
VERIFICATION_RETRY_DELAY=5000

# Logging configuration
LOG_VERIFICATION_RESULTS=true
DETAILED_VERIFICATION_LOGGING=false

# Improvement tracking
TRACK_IMPROVEMENTS=true
IMPROVEMENT_LOG_PATH=app/logs/improvements.log

# Discord notifications for verification issues
NOTIFY_VERIFICATION_ISSUES=false
VERIFICATION_DISCORD_WEBHOOK=

# =============================================================================
# MODEL RECOMMENDATIONS
# =============================================================================

# TRANSLATION MODELS:
# - claude-3-5-sonnet-20241022: Latest Claude 3.5, best for translation quality
# - claude-3-5-sonnet-20240620: Previous Claude 3.5, stable alternative
# - claude-3-opus-20240229: Most capable Claude 3, excellent for complex translations
# - claude-3-sonnet-20240229: Balanced performance and speed
# - claude-3-haiku-20240307: Fastest, good for simple translations

# VERIFICATION MODELS:
# - claude-sonnet-4-20250514: Best choice - Claude 4 with tool use capabilities
# - claude-3-opus-20240229: Excellent alternative for detailed analysis
# - claude-3-sonnet-20240229: Good balance of quality and speed
# - claude-3-haiku-20240307: Fastest option for basic verification

# IMPROVEMENT MODELS:
# - claude-3-5-sonnet-20241022: Best for nuanced improvements
# - claude-3-opus-20240229: Excellent for complex fixes
# - claude-3-sonnet-20240229: Good general-purpose improvement

# =============================================================================
# TEMPERATURE GUIDELINES
# =============================================================================

# TRANSLATION_TEMPERATURE:
# - 1.0: Creative, natural translations (recommended)
# - 0.7-0.9: Balanced creativity and consistency
# - 0.3-0.6: More consistent, less creative

# VERIFICATION_TEMPERATURE:
# - 0.3: Consistent, analytical verification (recommended)
# - 0.1-0.5: Very consistent analysis
# - 0.6-0.8: More flexible analysis

# IMPROVEMENT_TEMPERATURE:
# - 0.7: Balanced improvements (recommended)
# - 0.5-0.6: Conservative improvements
# - 0.8-1.0: More creative improvements

# =============================================================================
# COST OPTIMIZATION TIPS
# =============================================================================

# To reduce costs:
# 1. Use claude-3-haiku-20240307 for verification (fastest/cheapest)
# 2. Set CLAUDE4_VERIFICATION_MODE=sample with CLAUDE4_SAMPLE_RATE=0.1-0.3
# 3. Use lower MAX_TOKENS values if your content is shorter
# 4. Set CLAUDE4_VERIFICATION_ENABLED=false to disable verification entirely

# For maximum quality:
# 1. Use claude-sonnet-4-20250514 for verification
# 2. Use claude-3-5-sonnet-20241022 for translation and improvement
# 3. Set CLAUDE4_VERIFICATION_MODE=full
# 4. Set MIN_AVERAGE_SCORE=0.8 for stricter quality requirements