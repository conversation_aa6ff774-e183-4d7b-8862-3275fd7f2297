// @ts-check
/**
 * Claude 4 Translation Verification System
 * 
 * This module integrates Claude 4 with tool use capabilities to verify
 * translations from Claude 3.5 and suggest improvements.
 */

import { config } from 'dotenv';
config();
import Anthropic from '@anthropic-ai/sdk';
import fs from 'fs';
import path from 'path';
import { CLAUDE4_CONFIG, VERIFICATION_TOOLS } from './config.js';
import { VERIFICATION_TOOLS_MAP } from './verification-tools.js';

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  BG_BLUE: '\x1b[44m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_RED: '\x1b[41m',
};

// Initialize Claude 4 client
const claude4 = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY, // Using same key for now, will be separate for Claude 4
});

// Verification state
let verificationResults = [];
let improvementSuggestions = [];
let previousChunks = [];

/**
 * Main verification function for Claude 3.5 translations
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation from Claude 3.5
 * @param {Object} context - Additional context (character details, genre, etc.)
 * @param {number} chunkIndex - Index of current chunk
 * @returns {Promise<Object>} Verification result with scores and suggestions
 */
export async function verifyTranslation(sourceText, translatedText, context = {}, chunkIndex = 0) {
  if (!CLAUDE4_CONFIG.VERIFICATION_ENABLED) {
    console.info(`${COLORS.GRAY}[VERIFIER] Verification disabled, skipping...${COLORS.RESET}`);
    return { verified: false, reason: 'disabled' };
  }

  // Check if we should verify this chunk based on mode
  if (!shouldVerifyChunk(chunkIndex)) {
    console.info(`${COLORS.GRAY}[VERIFIER] Skipping verification for chunk ${chunkIndex} (sample mode)${COLORS.RESET}`);
    return { verified: false, reason: 'skipped_sample' };
  }

  console.info(`${COLORS.CYAN}[VERIFIER] Starting Claude 4 verification for chunk ${chunkIndex}...${COLORS.RESET}`);

  try {
    const verificationResult = await performComprehensiveVerification(
      sourceText,
      translatedText,
      context,
      chunkIndex
    );

    // Store results for tracking
    verificationResults.push(verificationResult);
    
    // Add to previous chunks for consistency checking
    previousChunks.push(translatedText);
    if (previousChunks.length > 10) { // Keep only last 10 chunks
      previousChunks.shift();
    }

    // Log results if enabled
    if (CLAUDE4_CONFIG.LOG_VERIFICATION_RESULTS) {
      await logVerificationResult(verificationResult);
    }

    // Send notifications if issues found
    if (verificationResult.hasIssues && CLAUDE4_CONFIG.NOTIFY_VERIFICATION_ISSUES) {
      await notifyVerificationIssues(verificationResult);
    }

    // Display overall verification decision
    displayVerificationDecision(verificationResult, chunkIndex);

    return verificationResult;

  } catch (error) {
    console.error(`${COLORS.RED}[VERIFIER] Error during verification: ${error.message}${COLORS.RESET}`);
    return { 
      verified: false, 
      reason: 'error', 
      error: error.message,
      chunkIndex 
    };
  }
}

/**
 * Perform comprehensive verification using Claude 4 with tools
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Comprehensive verification result
 */
async function performComprehensiveVerification(sourceText, translatedText, context, chunkIndex) {
  const verificationPrompt = createVerificationPrompt(sourceText, translatedText, context);
  
  let retries = 0;
  while (retries < CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
    try {
      const response = await claude4.messages.create({
        model: CLAUDE4_CONFIG.MODEL,
        max_tokens: CLAUDE4_CONFIG.MAX_TOKENS,
        temperature: CLAUDE4_CONFIG.TEMPERATURE,
        tools: VERIFICATION_TOOLS,
        messages: [
          {
            role: 'user',
            content: verificationPrompt
          }
        ]
      });

      // Process tool calls and responses
      const verificationResult = await processVerificationResponse(response, sourceText, translatedText, context, chunkIndex);
      
      return verificationResult;

    } catch (error) {
      retries++;
      console.warn(`${COLORS.YELLOW}[VERIFIER] Verification attempt ${retries} failed: ${error.message}${COLORS.RESET}`);
      
      if (retries >= CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
        throw error;
      }
      
      await sleep(CLAUDE4_CONFIG.VERIFICATION_RETRY_DELAY);
    }
  }
}

/**
 * Create verification prompt for Claude 4
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @returns {string} Formatted verification prompt
 */
function createVerificationPrompt(sourceText, translatedText, context) {
  return `You are Claude 4, an advanced AI with tool use capabilities, tasked with verifying and improving translations from Claude 3.5 Sonnet.

Your role is to:
1. Analyze the translation quality using the provided tools
2. Identify specific issues and areas for improvement
3. Provide actionable feedback for Claude 3.5 to learn from
4. Suggest concrete improvements

**Source Text (English):**
${sourceText}

**Translation to Verify (Polish):**
${translatedText}

**Context:**
- Character Details: ${context.characterDetails || 'Not provided'}
- Anime Genres: ${context.animeGenres || 'Not provided'}
- Additional Context: ${context.additionalContext || 'Not provided'}

Please use the available tools to perform a comprehensive analysis:

1. **translation_accuracy_checker**: Verify meaning preservation and accuracy
2. **cultural_context_validator**: Check cultural appropriateness and context
3. **polish_grammar_analyzer**: Analyze Polish grammar and style
4. **consistency_checker**: Check consistency with previous translations
5. **improvement_suggester**: Generate specific improvement suggestions

After using the tools, provide a summary with:
- Overall quality assessment
- Key strengths of the translation
- Priority issues to address
- Specific recommendations for Claude 3.5
- Learning points for future translations

Focus on being constructive and educational in your feedback.`;
}

/**
 * Process Claude 4's verification response and tool calls
 * @param {Object} response - Claude 4 API response
 * @param {string} sourceText - Original text
 * @param {string} translatedText - Translation
 * @param {Object} context - Context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Processed verification result
 */
async function processVerificationResponse(response, sourceText, translatedText, context, chunkIndex) {
  const result = {
    chunkIndex,
    timestamp: new Date().toISOString(),
    sourceText,
    translatedText,
    context,
    toolResults: {},
    overallAssessment: {},
    hasIssues: false,
    suggestions: [],
    learningPoints: []
  };

  // Process tool calls if any
  if (response.content) {
    for (const content of response.content) {
      if (content.type === 'tool_use') {
        const toolName = content.name;
        const toolInput = content.input;
        
        console.info(`${COLORS.BLUE}[VERIFIER] Executing tool: ${toolName}${COLORS.RESET}`);
        
        // Execute the tool
        if (VERIFICATION_TOOLS_MAP[toolName]) {
          try {
            const toolResult = VERIFICATION_TOOLS_MAP[toolName](toolInput);
            result.toolResults[toolName] = toolResult;

            // Display user-friendly result instead of raw JSON
            displayToolResult(toolName, toolResult);

          } catch (toolError) {
            console.error(`${COLORS.RED}[VERIFIER] Tool ${toolName} error: ${toolError.message}${COLORS.RESET}`);
            result.toolResults[toolName] = { error: toolError.message };
          }
        }
      } else if (content.type === 'text') {
        // Parse the text response for overall assessment
        result.overallAssessment.summary = content.text;
      }
    }
  }

  // Analyze results and determine if there are issues
  result.hasIssues = analyzeForIssues(result.toolResults);
  result.suggestions = extractSuggestions(result.toolResults);
  result.learningPoints = extractLearningPoints(result.toolResults);

  return result;
}

/**
 * Determine if verification should be performed for this chunk
 * @param {number} chunkIndex - Index of the chunk
 * @returns {boolean} Whether to verify this chunk
 */
function shouldVerifyChunk(chunkIndex) {
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'disabled') return false;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'full') return true;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'sample') {
    return Math.random() < CLAUDE4_CONFIG.SAMPLE_RATE;
  }
  return false;
}

/**
 * Analyze tool results to determine if there are issues based on average score
 * @param {Object} toolResults - Results from verification tools
 * @returns {boolean} Whether issues were found
 */
function analyzeForIssues(toolResults) {
  const scores = [];

  for (const [toolName, result] of Object.entries(toolResults)) {
    if (result.error) return true;

    // Collect all available scores
    if (result.overall_accuracy_score) scores.push(result.overall_accuracy_score);
    if (result.overall_fluency_score) scores.push(result.overall_fluency_score);
    if (result.overall_cultural_score) scores.push(result.overall_cultural_score);
    if (result.overall_consistency_score) scores.push(result.overall_consistency_score);
  }

  if (scores.length === 0) return false;

  // Calculate average score
  const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;

  // Single condition: check if average score is below threshold
  return averageScore < CLAUDE4_CONFIG.MIN_AVERAGE_SCORE;
}

/**
 * Extract suggestions from tool results
 * @param {Object} toolResults - Results from verification tools
 * @returns {Array} Array of suggestions
 */
function extractSuggestions(toolResults) {
  const suggestions = [];
  for (const [toolName, result] of Object.entries(toolResults)) {
    if (result.recommendations) {
      suggestions.push(...result.recommendations);
    }
  }
  return suggestions;
}

/**
 * Extract learning points from tool results
 * @param {Object} toolResults - Results from verification tools
 * @returns {Array} Array of learning points
 */
function extractLearningPoints(toolResults) {
  const learningPoints = [];
  for (const [toolName, result] of Object.entries(toolResults)) {
    if (result.learning_suggestions) {
      learningPoints.push(...result.learning_suggestions);
    }
  }
  return learningPoints;
}

/**
 * Log verification result to file
 * @param {Object} result - Verification result
 * @returns {Promise<void>}
 */
async function logVerificationResult(result) {
  const logEntry = {
    timestamp: result.timestamp,
    chunkIndex: result.chunkIndex,
    hasIssues: result.hasIssues,
    toolResults: result.toolResults,
    suggestions: result.suggestions,
    learningPoints: result.learningPoints
  };

  const logPath = 'app/logs/verification.log';
  const logLine = JSON.stringify(logEntry) + '\n';
  
  try {
    await fs.promises.appendFile(logPath, logLine);
  } catch (error) {
    console.error(`${COLORS.RED}[VERIFIER] Failed to log verification result: ${error.message}${COLORS.RESET}`);
  }
}

/**
 * Send notification about verification issues
 * @param {Object} result - Verification result with issues
 * @returns {Promise<void>}
 */
async function notifyVerificationIssues(result) {
  // Implementation would send Discord notification
  console.warn(`${COLORS.YELLOW}[VERIFIER] Issues found in chunk ${result.chunkIndex}, notification would be sent${COLORS.RESET}`);
}

/**
 * Get verification statistics
 * @returns {Object} Verification statistics
 */
export function getVerificationStats() {
  const totalVerifications = verificationResults.length;
  const issuesFound = verificationResults.filter(r => r.hasIssues).length;
  
  return {
    totalVerifications,
    issuesFound,
    successRate: totalVerifications > 0 ? (totalVerifications - issuesFound) / totalVerifications : 0,
    averageScores: calculateAverageScores(),
    commonIssues: identifyCommonIssues()
  };
}

/**
 * Calculate average scores across all verifications
 * @returns {Object} Average scores
 */
function calculateAverageScores() {
  // Implementation would calculate averages from verification results
  return {
    accuracy: 0.85,
    fluency: 0.88,
    cultural: 0.82
  };
}

/**
 * Identify common issues across verifications
 * @returns {Array} Common issues
 */
function identifyCommonIssues() {
  // Implementation would analyze patterns in verification results
  return ['Grammar inconsistencies', 'Cultural context preservation'];
}

/**
 * Display tool result in a user-friendly format
 * @param {string} toolName - Name of the tool
 * @param {Object} toolResult - Result from the tool
 * @returns {void}
 */
function displayToolResult(toolName, toolResult) {
  console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[VERIFIER] ${toolName.toUpperCase().replace(/_/g, ' ')} RESULTS${COLORS.RESET}`);

  switch (toolName) {
    case 'translation_accuracy_checker':
      displayAccuracyResults(toolResult);
      break;
    case 'cultural_context_validator':
      displayCulturalResults(toolResult);
      break;
    case 'polish_grammar_analyzer':
      displayGrammarResults(toolResult);
      break;
    case 'consistency_checker':
      displayConsistencyResults(toolResult);
      break;
    case 'improvement_suggester':
      displayImprovementResults(toolResult);
      break;
    default:
      console.info(`${COLORS.GRAY}[VERIFIER] Unknown tool result format${COLORS.RESET}`);
  }

  // Display average score for this tool
  displayToolAverageScore(toolResult);
  console.info(''); // Add spacing
}

/**
 * Display average score for a tool result
 * @param {Object} toolResult - Tool result
 */
function displayToolAverageScore(toolResult) {
  const scores = [];

  // Collect all scores from the tool result
  if (toolResult.overall_accuracy_score) scores.push(toolResult.overall_accuracy_score);
  if (toolResult.overall_fluency_score) scores.push(toolResult.overall_fluency_score);
  if (toolResult.overall_cultural_score) scores.push(toolResult.overall_cultural_score);
  if (toolResult.overall_consistency_score) scores.push(toolResult.overall_consistency_score);

  if (scores.length > 0) {
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const scoreColor = averageScore >= 0.8 ? COLORS.GREEN : averageScore >= 0.7 ? COLORS.YELLOW : COLORS.RED;
    console.info(`${scoreColor}📊 Tool Average Score: ${(averageScore * 100).toFixed(1)}%${COLORS.RESET}`);
  }
}

/**
 * Display accuracy checker results in a user-friendly format
 * @param {Object} result - Accuracy checker result
 */
function displayAccuracyResults(result) {
  console.info(`${COLORS.GREEN}📊 Overall Accuracy Score: ${(result.overall_accuracy_score * 100).toFixed(1)}%${COLORS.RESET}`);

  if (result.meaning_preservation) {
    console.info(`${COLORS.CYAN}   🎯 Meaning Preservation: ${(result.meaning_preservation.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.content_completeness) {
    console.info(`${COLORS.CYAN}   📝 Content Completeness: ${(result.content_completeness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.tone_consistency) {
    console.info(`${COLORS.CYAN}   🎭 Tone Consistency: ${(result.tone_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.recommendations && result.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    result.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }
}

/**
 * Display cultural context results in a user-friendly format
 * @param {Object} result - Cultural context result
 */
function displayCulturalResults(result) {
  console.info(`${COLORS.GREEN}🌍 Overall Cultural Score: ${(result.overall_cultural_score * 100).toFixed(1)}%${COLORS.RESET}`);

  if (result.cultural_references) {
    console.info(`${COLORS.CYAN}   🏛️ Cultural References: ${(result.cultural_references.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.honorifics_handling) {
    console.info(`${COLORS.CYAN}   🙏 Honorifics Handling: ${(result.honorifics_handling.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.genre_appropriateness) {
    console.info(`${COLORS.CYAN}   🎬 Genre Appropriateness: ${(result.genre_appropriateness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.character_voice) {
    console.info(`${COLORS.CYAN}   🗣️ Character Voice: ${(result.character_voice.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.recommendations && result.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    result.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }
}

/**
 * Display grammar analyzer results in a user-friendly format
 * @param {Object} result - Grammar analyzer result
 */
function displayGrammarResults(result) {
  console.info(`${COLORS.GREEN}📚 Overall Fluency Score: ${(result.overall_fluency_score * 100).toFixed(1)}%${COLORS.RESET}`);

  if (result.grammar_correctness) {
    console.info(`${COLORS.CYAN}   ✅ Grammar Correctness: ${(result.grammar_correctness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.sentence_structure) {
    console.info(`${COLORS.CYAN}   🏗️ Sentence Structure: ${(result.sentence_structure.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.vocabulary_appropriateness) {
    console.info(`${COLORS.CYAN}   📖 Vocabulary Appropriateness: ${(result.vocabulary_appropriateness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.punctuation_formatting) {
    console.info(`${COLORS.CYAN}   ✏️ Punctuation & Formatting: ${(result.punctuation_formatting.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.recommendations && result.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    result.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }
}

/**
 * Display consistency checker results in a user-friendly format
 * @param {Object} result - Consistency checker result
 */
function displayConsistencyResults(result) {
  console.info(`${COLORS.GREEN}🔄 Overall Consistency Score: ${(result.overall_consistency_score * 100).toFixed(1)}%${COLORS.RESET}`);

  if (result.terminology_consistency) {
    console.info(`${COLORS.CYAN}   📚 Terminology Consistency: ${(result.terminology_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.character_name_consistency) {
    console.info(`${COLORS.CYAN}   👤 Character Name Consistency: ${(result.character_name_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.style_consistency) {
    console.info(`${COLORS.CYAN}   🎨 Style Consistency: ${(result.style_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  if (result.recommendations && result.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    result.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }
}

/**
 * Display improvement suggester results in a user-friendly format
 * @param {Object} result - Improvement suggester result
 */
function displayImprovementResults(result) {
  console.info(`${COLORS.GREEN}🚀 Priority Level: ${result.priority_level?.toUpperCase() || 'UNKNOWN'}${COLORS.RESET}`);

  if (result.specific_improvements && result.specific_improvements.length > 0) {
    console.info(`${COLORS.MAGENTA}   🔧 Specific Improvements:${COLORS.RESET}`);
    result.specific_improvements.forEach(improvement => {
      console.info(`${COLORS.MAGENTA}      • ${improvement.issue}: ${improvement.suggestion}${COLORS.RESET}`);
    });
  }

  if (result.alternative_translations && result.alternative_translations.length > 0) {
    console.info(`${COLORS.BLUE}   🔄 Alternative Translations:${COLORS.RESET}`);
    result.alternative_translations.forEach(alt => {
      console.info(`${COLORS.BLUE}      • ${alt}${COLORS.RESET}`);
    });
  }

  if (result.learning_suggestions && result.learning_suggestions.length > 0) {
    console.info(`${COLORS.CYAN}   🎓 Learning Suggestions:${COLORS.RESET}`);
    result.learning_suggestions.forEach(suggestion => {
      console.info(`${COLORS.CYAN}      • ${suggestion}${COLORS.RESET}`);
    });
  }

  if (result.recommendations && result.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    result.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }
}

/**
 * Display overall verification decision
 * @param {Object} verificationResult - Verification result
 * @param {number} chunkIndex - Chunk index
 */
function displayVerificationDecision(verificationResult, chunkIndex) {
  // Calculate overall average score
  const allScores = [];

  if (verificationResult.toolResults) {
    for (const result of Object.values(verificationResult.toolResults)) {
      if (result.overall_accuracy_score) allScores.push(result.overall_accuracy_score);
      if (result.overall_fluency_score) allScores.push(result.overall_fluency_score);
      if (result.overall_cultural_score) allScores.push(result.overall_cultural_score);
      if (result.overall_consistency_score) allScores.push(result.overall_consistency_score);
    }
  }

  if (allScores.length > 0) {
    const overallAverage = allScores.reduce((sum, score) => sum + score, 0) / allScores.length;
    const passed = overallAverage >= CLAUDE4_CONFIG.MIN_AVERAGE_SCORE;

    console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[VERIFICATION DECISION]${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}📊 Overall Average Score: ${(overallAverage * 100).toFixed(1)}%${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}🎯 Required Threshold: ${(CLAUDE4_CONFIG.MIN_AVERAGE_SCORE * 100).toFixed(1)}%${COLORS.RESET}`);

    if (passed) {
      console.info(`${COLORS.GREEN}✅ VERIFICATION PASSED for chunk ${chunkIndex + 1}${COLORS.RESET}`);
    } else {
      console.info(`${COLORS.RED}❌ VERIFICATION FAILED for chunk ${chunkIndex + 1} - Improvement needed${COLORS.RESET}`);

      // Show number of line-specific issues if any
      const lineIssues = extractLineSpecificIssuesCount(verificationResult);
      if (lineIssues > 0) {
        console.info(`${COLORS.YELLOW}⚠️ ${lineIssues} line-specific issues identified for improvement${COLORS.RESET}`);
      }
    }
  } else {
    console.info(`${COLORS.YELLOW}⚠️ No scores available for verification decision${COLORS.RESET}`);
  }

  console.info(''); // Add spacing
}

/**
 * Extract count of line-specific issues from verification result
 * @param {Object} verificationResult - Verification result
 * @returns {number} Number of line-specific issues
 */
function extractLineSpecificIssuesCount(verificationResult) {
  let count = 0;

  if (verificationResult.toolResults) {
    for (const result of Object.values(verificationResult.toolResults)) {
      if (result.meaning_preservation?.issues) count += result.meaning_preservation.issues.length;
      if (result.content_completeness?.missing_elements) count += result.content_completeness.missing_elements.length;
      if (result.tone_consistency?.style_issues) count += result.tone_consistency.style_issues.length;
    }
  }

  return count;
}

/**
 * Sleep utility function
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export default {
  verifyTranslation,
  getVerificationStats
};
